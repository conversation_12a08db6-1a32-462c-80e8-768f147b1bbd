[mypy]
# Global options
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
show_error_codes = True
show_column_numbers = True
show_error_context = True

# Per-module options
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

# Third-party libraries without stubs
[mypy-chainlit.*]
ignore_missing_imports = True

[mypy-structlog.*]
ignore_missing_imports = True

[mypy-supabase.*]
ignore_missing_imports = True

[mypy-openai.*]
ignore_missing_imports = True

[mypy-beautifulsoup4.*]
ignore_missing_imports = True

[mypy-bs4.*]
ignore_missing_imports = True

[mypy-aiohttp.*]
ignore_missing_imports = True
