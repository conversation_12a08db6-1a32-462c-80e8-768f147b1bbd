import json
import os

import requests
from dotenv import load_dotenv

load_dotenv()
rapidapi_key = os.getenv("RAPIDAPI_KEY")

url = "https://api-football-v1.p.rapidapi.com/v3/teams"

headers = {
    "x-rapidapi-key": rapidapi_key,
    "x-rapidapi-host": "api-football-v1.p.rapidapi.com",
}

payload = {
    "league": "39",
    "season": "2023",
    # "timezone": "Europe/London",
    # "date": "2023-02-23",
}

try:
    response = requests.request("GET", url, headers=headers, params=payload)
    response.raise_for_status()

    print(f"Response Status Code: {response.status_code}")
    print(json.dumps(response.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"An error occurred: {e}")
