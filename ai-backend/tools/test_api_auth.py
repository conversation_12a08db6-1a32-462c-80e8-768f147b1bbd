import logging
import os
from typing import Any

import aiohttp
from dotenv import load_dotenv

from utils.security import sanitize_log_input, sanitize_multiple_log_inputs


load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIAuth:
    """Authentication client for API-Football from RapidAPI."""

    def __init__(self, api_key: str | None = None):
        self.api_key = api_key or os.getenv("RAPIDAPI_KEY")
        self.base_url = "https://api-football-v1.p.rapidapi.com/v3"

        if self.api_key:
            self.headers = {
                "x-rapidapi-key": self.api_key,
                "x-rapidapi-host": "api-football-v1.p.rapidapi.com",
                "Content-Type": "application/json",
            }
        else:
            self.headers = {}

        self.session: aiohttp.ClientSession | None = None

    async def __aenter__(self) -> "APIAuth":
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: Any,
    ) -> None:
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("API session closed.")
            return
        
        logger.error("API session not initialized.")    
        return



payload = {
    "league": "39",
    "season": "2023",
    # "timezone": "Europe/London",
    # "date": "2023-02-23",
}

session = aiohttp.ClientSession() | None = None

url = base_url + "/teams"


try:
    async with session.get(url, headers=headers, params=payload) as response:
        logger.info(
            f"Requesting fixtures with params: {payload}. Status: {response.status}"
        )

        response.raise_for_status()

        response_json = await response.json()

        response_json_ = response_json.get("response", [])
        if not isinstance(data_response, list):
            data_response = []
        return data_response

except aiohttp.ClientError as e:
    logger.error(f"An error occurred while calling API-Football: {e}")
    return []

# try:
#     response = requests.request("GET", url, headers=headers, params=payload)
#     response.raise_for_status()

#     print(f"Response Status Code: {response.status_code}")
#     print(json.dumps(response.json(), indent=2))

# except requests.exceptions.RequestException as e:
#     print(f"An error occurred: {e}")
