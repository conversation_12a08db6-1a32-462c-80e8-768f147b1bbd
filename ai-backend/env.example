# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-nano

# Supabase Configuration (for AI agents to publish articles)
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# FastAPI Configuration
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=8000
FASTAPI_RELOAD=true

# Chainlit Configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8001

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Development
DEBUG=true
ENVIRONMENT=development

# API-Football Configuration (RapidAPI)
RAPIDAPI_KEY=your_rapidapi_key_here
API_FOOTBALL_BASE_URL=https://api-football-v1.p.rapidapi.com/v3

# Football Settings
DEFAULT_SEASON=2024
DEFAULT_LEAGUES=premier_league,la_liga,serie_a,bundesliga,ligue_1
