services:
  ai-backend:
    build:
      context: ./ai-backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./ai-backend:/app
    env_file:
      - ./ai-backend/.env
    environment:
      - FASTAPI_RELOAD=true
      - DEBUG=true
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./web:/app
      - /app/node_modules
    env_file:
      - ./web/.env.local
    environment:
      - NODE_ENV=development
    command: npm run dev
