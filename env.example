# =============================================================================
# Sport Scribe - Environment Variables Template
# =============================================================================
# Copy this file and rename to .env, then fill in your actual values
# Never commit .env files with real secrets to version control

# =============================================================================
# WEB PLATFORM (Next.js) ENVIRONMENT VARIABLES
# =============================================================================

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Next.js Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Sport Scribe

# Development
NODE_ENV=development

# =============================================================================
# AI BACKEND (Python) ENVIRONMENT VARIABLES
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORG_ID=your_openai_org_id

# Supabase Configuration (for AI agents to publish articles)
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# FastAPI Configuration
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=8000
FASTAPI_RELOAD=true

# Chainlit Configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8001

# Sports Data APIs (add your API keys)
RAPIDAPI_KEY=your_rapidapi_key_here
API_FOOTBALL_BASE_URL=https://api-football-v1.p.rapidapi.com/v3

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Development
DEBUG=true
ENVIRONMENT=development

# =============================================================================
# SHARED CONFIGURATION
# =============================================================================

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/sport_scribe

# Redis (for caching and real-time features)
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_id

# =============================================================================
# DEPLOYMENT ENVIRONMENT VARIABLES
# =============================================================================

# Production URLs
PRODUCTION_WEB_URL=https://your-app.vercel.app
PRODUCTION_API_URL=https://your-api.render.com

# CI/CD
VERCEL_TOKEN=your_vercel_token
RENDER_API_KEY=your_render_api_key
