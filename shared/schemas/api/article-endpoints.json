{"endpoints": {"articles": {"list": {"method": "GET", "path": "/api/articles", "description": "Get a paginated list of articles", "query_parameters": {"page": {"type": "integer", "default": 1, "description": "Page number for pagination"}, "limit": {"type": "integer", "default": 20, "maximum": 100, "description": "Number of articles per page"}, "sport": {"type": "string", "description": "Filter by sport"}, "league": {"type": "string", "description": "Filter by league"}, "status": {"type": "string", "enum": ["draft", "published", "archived", "scheduled"], "description": "Filter by article status"}, "author": {"type": "string", "description": "Filter by author name"}, "tags": {"type": "string", "description": "Comma-separated list of tags to filter by"}, "date_from": {"type": "string", "format": "date", "description": "Filter articles published after this date"}, "date_to": {"type": "string", "format": "date", "description": "Filter articles published before this date"}, "search": {"type": "string", "description": "Search in article titles and content"}, "sort": {"type": "string", "enum": ["created_at", "published_at", "title", "reading_time"], "default": "published_at", "description": "Field to sort by"}, "order": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Article"}}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata"}}}}, "get": {"method": "GET", "path": "/api/articles/{id}", "description": "Get a specific article by ID", "path_parameters": {"id": {"type": "string", "format": "uuid", "description": "Article ID"}}, "query_parameters": {"include_metadata": {"type": "boolean", "default": false, "description": "Include article metadata"}, "include_related": {"type": "boolean", "default": false, "description": "Include related articles"}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ArticleWithDetails"}}}}, "create": {"method": "POST", "path": "/api/articles", "description": "Create a new article", "authentication": "required", "permissions": ["article:create"], "request_body": {"type": "object", "required": ["title", "content", "sport", "league"], "properties": {"title": {"type": "string", "maxLength": 500, "description": "Article title"}, "content": {"type": "string", "description": "Article content in markdown"}, "summary": {"type": "string", "maxLength": 1000, "description": "Article summary"}, "sport": {"type": "string", "description": "Sport category"}, "league": {"type": "string", "description": "League name"}, "game_id": {"type": "string", "format": "uuid", "description": "Related game ID"}, "status": {"type": "string", "enum": ["draft", "published", "scheduled"], "default": "draft", "description": "Article status"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Article tags"}, "featured_image_url": {"type": "string", "format": "uri", "description": "Featured image URL"}, "seo_keywords": {"type": "array", "items": {"type": "string"}, "description": "SEO keywords"}, "published_at": {"type": "string", "format": "date-time", "description": "Scheduled publish time (for scheduled articles)"}}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/Article"}}}}, "update": {"method": "PUT", "path": "/api/articles/{id}", "description": "Update an existing article", "authentication": "required", "permissions": ["article:update"], "path_parameters": {"id": {"type": "string", "format": "uuid", "description": "Article ID"}}, "request_body": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 500}, "content": {"type": "string"}, "summary": {"type": "string", "maxLength": 1000}, "status": {"type": "string", "enum": ["draft", "published", "archived", "scheduled"]}, "tags": {"type": "array", "items": {"type": "string"}}, "featured_image_url": {"type": "string", "format": "uri"}, "seo_keywords": {"type": "array", "items": {"type": "string"}}, "published_at": {"type": "string", "format": "date-time"}}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/Article"}}}}, "delete": {"method": "DELETE", "path": "/api/articles/{id}", "description": "Delete an article", "authentication": "required", "permissions": ["article:delete"], "path_parameters": {"id": {"type": "string", "format": "uuid", "description": "Article ID"}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}, "generate": {"method": "POST", "path": "/api/articles/generate", "description": "Generate a new article using AI", "authentication": "required", "permissions": ["article:generate"], "request_body": {"type": "object", "required": ["game_id", "focus_type"], "properties": {"game_id": {"type": "string", "format": "uuid", "description": "Game ID to write about"}, "focus_type": {"type": "string", "enum": ["game_recap", "player_spotlight", "team_analysis", "season_preview", "trade_news"], "description": "Type of article to generate"}, "target_length": {"type": "integer", "minimum": 500, "maximum": 5000, "default": 1500, "description": "Target article length in words"}, "tone": {"type": "string", "enum": ["professional", "casual", "analytical", "dramatic"], "default": "professional", "description": "Article tone"}, "include_stats": {"type": "boolean", "default": true, "description": "Include game statistics"}, "include_quotes": {"type": "boolean", "default": false, "description": "Include player/coach quotes"}}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["pending", "in_progress"]}, "estimated_completion": {"type": "string", "format": "date-time"}}}}}}, "popular": {"method": "GET", "path": "/api/articles/popular", "description": "Get popular articles based on views and engagement", "query_parameters": {"period": {"type": "string", "enum": ["day", "week", "month", "year"], "default": "week", "description": "Time period for popularity calculation"}, "sport": {"type": "string", "description": "Filter by sport"}, "limit": {"type": "integer", "default": 10, "maximum": 50, "description": "Number of articles to return"}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Article"}, {"type": "object", "properties": {"view_count": {"type": "integer"}, "comment_count": {"type": "integer"}, "engagement_score": {"type": "number"}}}]}}}}}, "related": {"method": "GET", "path": "/api/articles/{id}/related", "description": "Get articles related to a specific article", "path_parameters": {"id": {"type": "string", "format": "uuid", "description": "Article ID"}}, "query_parameters": {"limit": {"type": "integer", "default": 5, "maximum": 20, "description": "Number of related articles to return"}}, "response": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Article"}}}}}}}, "components": {"schemas": {"Article": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "content": {"type": "string"}, "summary": {"type": "string"}, "author": {"type": "string"}, "sport": {"type": "string"}, "league": {"type": "string"}, "game_id": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["draft", "published", "archived", "scheduled"]}, "published_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "featured_image_url": {"type": "string", "format": "uri"}, "reading_time_minutes": {"type": "integer"}, "seo_keywords": {"type": "array", "items": {"type": "string"}}, "byline": {"type": "string"}}}, "ArticleWithDetails": {"allOf": [{"$ref": "#/components/schemas/Article"}, {"type": "object", "properties": {"metadata": {"type": "object", "properties": {"word_count": {"type": "integer"}, "readability_score": {"type": "number"}, "sentiment_score": {"type": "number"}, "topics": {"type": "array", "items": {"type": "string"}}, "entities": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "mentions": {"type": "integer"}, "confidence": {"type": "number"}}}}}}, "related_articles": {"type": "array", "items": {"$ref": "#/components/schemas/Article"}}}}]}, "PaginationMetadata": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}, "has_next": {"type": "boolean"}, "has_previous": {"type": "boolean"}}}}, "errors": {"NotFound": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "ARTICLE_NOT_FOUND"}, "message": {"type": "string", "example": "Article not found"}}}}}, "ValidationError": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "VALIDATION_ERROR"}, "message": {"type": "string", "example": "Validation failed"}, "field_errors": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}, "code": {"type": "string"}}}}}}}}, "Unauthorized": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Authentication required"}}}}}, "Forbidden": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "FORBIDDEN"}, "message": {"type": "string", "example": "Insufficient permissions"}}}}}}}}