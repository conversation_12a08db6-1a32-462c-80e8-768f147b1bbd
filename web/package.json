{"name": "sport-scribe-web", "version": "0.1.0", "private": true, "dependencies": {"@heroui/react": "^2.0.0", "@heroui/theme": "^2.4.17", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.0", "framer-motion": "^11.9.0", "lucide-react": "^0.525.0", "next": "^14.2.30", "next-themes": "^0.4.6", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.0", "eslint-config-prettier": "^9.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "tailwindcss": "^3.4.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo 'No tests specified yet' && exit 0", "db:setup": "supabase db reset && npm run db:seed", "generate:types": "supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID > lib/supabase/database.types.ts", "db:seed": "python3 ../scripts/seed-data.py"}}