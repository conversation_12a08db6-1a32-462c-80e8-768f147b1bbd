---
extends: default

rules:
  line-length:
    max: 120
    level: warning
  comments:
    min-spaces-from-content: 1
  document-start: disable
  truthy:
    allowed-values: ['true', 'false', 'on', 'off']
  indentation:
    spaces: 2
  brackets:
    max-spaces-inside: 1
  braces:
    max-spaces-inside: 1

ignore: |
  web/node_modules/
  .git/
  .cache/
  .pytest_cache/
  __pycache__/
  *.pyc
  *.pyo
  *.egg-info/
  dist/
  build/
  # Ignore disabled workflows
  .github/workflows/*.disabled
