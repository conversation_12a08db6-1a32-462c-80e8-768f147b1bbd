---
name: Security Audit

on:
  schedule:
    # Run every Sunday at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  dependency-review:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Dependency Review
        uses: actions/dependency-review-action@72eb03d02c7872a771aacd928f3123ac62ad6d3a
        with:
          fail-on-severity: moderate

  ai-backend-security:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        working-directory: ai-backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Run security checks with bandit
        working-directory: ai-backend
        run: bandit -r agents/ tools/ config/ utils/ -f json -o bandit-report.json || true

      - name: Upload bandit report
        uses: actions/upload-artifact@v4
        with:
          name: bandit-security-report
          path: ai-backend/bandit-report.json

  web-security:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: web/package-lock.json

      - name: Install dependencies
        working-directory: web
        run: npm ci

      - name: Run npm audit
        working-directory: web
        run: npm audit --audit-level=moderate || true

  trivy-scan:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@7c2007bcb556501da015201bcba5aa14069b74e2
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: actions/upload-artifact@v4
        with:
          name: trivy-results
          path: trivy-results.sarif
