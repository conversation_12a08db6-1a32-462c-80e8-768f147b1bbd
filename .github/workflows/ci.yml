---
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  ai-backend:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('ai-backend/requirements*.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        working-directory: ai-backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Lint with ruff
        working-directory: ai-backend
        run: ruff check .

      - name: Format check with black
        working-directory: ai-backend
        run: black --check .

      - name: Type check with mypy
        working-directory: ai-backend
        run: mypy agents/ tools/ config/ utils/ --strict

      - name: Test with pytest
        working-directory: ai-backend
        run: |
          pytest --cov=agents --cov=tools --cov=config --cov=utils \
                 --cov-report=xml --cov-report=term-missing \
                 tests/

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673
        with:
          file: ai-backend/coverage.xml
          flags: ai-backend
          name: ai-backend-coverage

  web:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: web/package-lock.json

      - name: Install dependencies
        working-directory: web
        run: npm ci

      - name: Lint with ESLint
        working-directory: web
        run: npm run lint

      - name: Type check
        working-directory: web
        run: npx tsc --noEmit

      - name: Run tests
        working-directory: web
        run: npm test

      - name: Build
        working-directory: web
        run: npm run build
        env:
          # Use dummy values for build
          NEXT_PUBLIC_SUPABASE_URL: https://dummy.supabase.co
          NEXT_PUBLIC_SUPABASE_ANON_KEY: dummy_key

  security:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
      actions: read

    steps:
      - uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@7c2007bcb556501da015201bcba5aa14069b74e2
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
