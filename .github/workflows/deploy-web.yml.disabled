---
name: Deploy Web Platform

on:
  push:
    branches: [ main ]
    paths:
      - 'web/**'
      - 'shared/**'
      - '.github/workflows/deploy-web.yml'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Install Vercel CLI
      run: npm install --global vercel@latest

    - name: Install dependencies
      working-directory: web
      run: npm ci

    - name: Run tests
      working-directory: web
      run: npm test
      env:
        NEXT_PUBLIC_SUPABASE_URL: https://dummy.supabase.co
        NEXT_PUBLIC_SUPABASE_ANON_KEY: dummy_key

    - name: Build project
      working-directory: web
      run: npm run build
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

    - name: Deploy to Vercel
      working-directory: web
      run: vercel deploy --prod --token=${{ secrets.VERCEL_TOKEN }}
      env:
        VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
        VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

    - name: Notify deployment
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ Web Platform deployed successfully to Vercel"
        else
          echo "❌ Web Platform deployment failed"
        fi
