version: 2
updates:
  # Python dependencies (AI Backend)
  - package-ecosystem: "pip"
    directory: "/ai-backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "vibing-ai"
    commit-message:
      prefix: "deps(ai-backend)"
      include: "scope"
    labels:
      - "dependencies"
      - "ai-backend"
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    ignore:
      # Ignore major version updates for stable packages
      - dependency-name: "openai"
        update-types: ["version-update:semver-major"]

  # Node.js dependencies (Web Platform)
  - package-ecosystem: "npm"
    directory: "/web"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "vibing-ai"
    commit-message:
      prefix: "deps(web)"
      include: "scope"
    labels:
      - "dependencies"
      - "web"
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    ignore:
      # Ignore major version updates for React and Next.js
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "next"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "vibing-ai"
    commit-message:
      prefix: "deps(actions)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"

  # Docker
  - package-ecosystem: "docker"
    directory: "/ai-backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "vibing-ai"
    commit-message:
      prefix: "deps(docker)"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
