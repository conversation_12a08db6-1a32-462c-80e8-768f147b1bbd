---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'triage']
assignees: ''

---

## Bug Description

A clear and concise description of what the bug is.

## Steps to Reproduce

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior

A clear and concise description of what you expected to happen.

## Actual Behavior

A clear and concise description of what actually happened.

## Screenshots

If applicable, add screenshots to help explain your problem.

## Environment

**Platform:** (AI Backend / Web Platform / Both)

**AI Backend:**

- Python version: [e.g. 3.11]
- OpenAI SDK version: [e.g. 1.0.0]
- OS: [e.g. Ubuntu 20.04]

**Web Platform:**

- Browser: [e.g. chrome, safari]
- Version: [e.g. 22]
- Node.js version: [e.g. 18.17.0]
- Next.js version: [e.g. 14.0.0]

## Additional Context

Add any other context about the problem here.

## Error Logs

```text
Paste any relevant error logs here
```

## Possible Solution

If you have a suggestion for how to fix the bug, please describe it here.
