repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-json
      - id: check-toml
      - id: mixed-line-ending

  # Python linting and formatting
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.1
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        files: ^ai-backend/.*\.py$

  # Python formatting and type checking disabled due to version conflicts
  # Run manually: black ai-backend/ && mypy ai-backend/
  # - repo: https://github.com/psf/black
  #   rev: 25.1.0
  #   hooks:
  #     - id: black
  #       files: ^ai-backend/.*\.py$

  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.16.1
  #   hooks:
  #     - id: mypy
  #       files: ^ai-backend/.*\.py$
  #       additional_dependencies: [types-requests, types-PyYAML]

  # TypeScript/JavaScript linting and formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        files: ^web/.*\.(ts|tsx|js|jsx|json|css|md)$

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.45.0
    hooks:
      - id: markdownlint
        args: [--fix]
        files: \.md$
        exclude: ^(docs/|\.github/|CONTRIBUTING\.md|ai-backend/README\.md|shared/README\.md|web/README\.md|.*node_modules/)

# Security checks disabled - requires manual setup
# - repo: https://github.com/Yelp/detect-secrets
#   rev: v1.5.0
#   hooks:
#     - id: detect-secrets
#       args: [--baseline, .secrets.baseline]
#       exclude: package.lock.json
